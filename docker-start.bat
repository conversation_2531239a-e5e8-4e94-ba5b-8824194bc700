@echo off
title ATMA Backend - Docker Deployment

echo ========================================
echo ATMA Backend - Docker Deployment
echo ========================================
echo.

REM Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running or not installed!
    echo Please start Docker Desktop and try again.
    pause
    exit /b 1
)

REM Check if docker-compose is available
docker-compose version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker Compose is not available!
    echo Please install Docker Compose and try again.
    pause
    exit /b 1
)

echo Docker is running. Starting ATMA Backend services...
echo.

REM Stop any existing containers
echo Stopping existing containers...
docker-compose down

echo.
echo Building and starting services...
echo This may take a few minutes on first run...
echo.

REM Start services
docker-compose up -d --build

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to start services!
    echo Check the logs above for details.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Services Started Successfully!
echo ========================================
echo.

REM Wait a moment for services to initialize
echo Waiting for services to initialize...
timeout /t 10 /nobreak >nul

REM Check service status
echo Checking service status...
docker-compose ps

echo.
echo ========================================
echo ATMA Backend is now running!
echo ========================================
echo.
echo Service URLs:
echo - API Gateway (Main Entry): http://localhost:3000
echo - Auth Service: http://localhost:3001
echo - Archive Service: http://localhost:3002
echo - Assessment Service: http://localhost:3003
echo - Notification Service: http://localhost:3005
echo - RabbitMQ Management: http://localhost:15672 (guest/guest)
echo.
echo Database:
echo - PostgreSQL: localhost:5432 (postgres/password)
echo - Redis: localhost:6379
echo.
echo Logs:
echo - View all logs: docker-compose logs -f
echo - View specific service: docker-compose logs -f [service-name]
echo.
echo To stop all services: docker-compose down
echo.

REM Test API Gateway health
echo Testing API Gateway health...
timeout /t 5 /nobreak >nul
curl -s http://localhost:3000/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ API Gateway is responding
) else (
    echo ⚠ API Gateway may still be starting up
    echo   Check logs with: docker-compose logs -f api-gateway
)

echo.
echo Setup complete! Press any key to exit...
pause >nul
