@echo off
title ATMA Backend - Docker Stop

echo ========================================
echo ATMA Backend - Stopping Services
echo ========================================
echo.

REM Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running!
    echo Services may already be stopped.
    pause
    exit /b 1
)

echo Stopping all ATMA Backend services...
echo.

REM Stop and remove containers
docker-compose down

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo All services stopped successfully!
    echo ========================================
    echo.
    echo Data volumes are preserved.
    echo To remove all data, run: docker-compose down -v
    echo.
) else (
    echo.
    echo ERROR: Failed to stop some services!
    echo Check Docker Desktop and try again.
    echo.
)

echo Press any key to exit...
pause >nul
