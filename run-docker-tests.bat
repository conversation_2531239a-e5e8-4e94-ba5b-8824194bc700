@echo off
title ATMA Backend - Complete Docker Testing Suite

echo ========================================
echo ATMA Backend - Complete Docker Testing
echo ========================================
echo.

REM Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running!
    echo Please start Docker Desktop and try again.
    pause
    exit /b 1
)

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH!
    echo Please install Node.js and try again.
    pause
    exit /b 1
)

REM Check if services are running
echo Checking if ATMA services are running...
docker-compose ps | findstr "Up" >nul
if %errorlevel% neq 0 (
    echo WARNING: ATMA services don't appear to be running.
    echo Would you like to start them now? (y/n)
    set /p start_services=
    if /i "%start_services%"=="y" (
        echo Starting ATMA services...
        docker-compose up -d
        echo Waiting for services to initialize...
        timeout /t 30 /nobreak >nul
    ) else (
        echo Please start the services first with: docker-compose up -d
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo Available Test Options:
echo ========================================
echo 1. Quick Infrastructure Test (Docker containers, health checks)
echo 2. API Functionality Test (Complete API testing)
echo 3. Load Performance Test (Performance and scalability)
echo 4. Continuous Monitoring (Real-time monitoring dashboard)
echo 5. Complete Test Suite (All tests in sequence)
echo 6. Exit
echo.

set /p test_choice=Please select a test option (1-6): 

if "%test_choice%"=="1" goto :infrastructure_test
if "%test_choice%"=="2" goto :api_test
if "%test_choice%"=="3" goto :load_test
if "%test_choice%"=="4" goto :monitoring
if "%test_choice%"=="5" goto :complete_test
if "%test_choice%"=="6" goto :exit
goto :invalid_choice

:infrastructure_test
echo.
echo ========================================
echo Running Infrastructure Test...
echo ========================================
call docker-test.bat
goto :end

:api_test
echo.
echo ========================================
echo Running API Functionality Test...
echo ========================================
cd testing
npm install --silent
node docker-api-test.js
cd ..
goto :end

:load_test
echo.
echo ========================================
echo Running Load Performance Test...
echo ========================================
echo This test will simulate multiple concurrent users.
echo It may take a few minutes to complete.
echo.
set /p confirm_load=Continue with load test? (y/n): 
if /i not "%confirm_load%"=="y" goto :end

cd testing
npm install --silent
node docker-load-test.js
cd ..
goto :end

:monitoring
echo.
echo ========================================
echo Starting Continuous Monitoring...
echo ========================================
echo This will start a real-time monitoring dashboard.
echo Press Ctrl+C to stop monitoring.
echo.
pause
cd testing
npm install --silent
node docker-monitor.js
cd ..
goto :end

:complete_test
echo.
echo ========================================
echo Running Complete Test Suite...
echo ========================================
echo This will run all tests in sequence:
echo 1. Infrastructure Test
echo 2. API Functionality Test  
echo 3. Load Performance Test
echo.
set /p confirm_complete=Continue with complete test suite? (y/n): 
if /i not "%confirm_complete%"=="y" goto :end

echo.
echo [1/3] Running Infrastructure Test...
echo =====================================
call docker-test.bat
if %errorlevel% neq 0 (
    echo Infrastructure test failed! Stopping test suite.
    goto :end
)

echo.
echo [2/3] Running API Functionality Test...
echo =======================================
cd testing
npm install --silent
node docker-api-test.js
set api_test_result=%errorlevel%
cd ..

if %api_test_result% neq 0 (
    echo API test failed! Continuing with load test...
)

echo.
echo [3/3] Running Load Performance Test...
echo =====================================
cd testing
node docker-load-test.js
set load_test_result=%errorlevel%
cd ..

echo.
echo ========================================
echo COMPLETE TEST SUITE RESULTS
echo ========================================
echo Infrastructure Test: PASSED
if %api_test_result% equ 0 (
    echo API Functionality Test: PASSED
) else (
    echo API Functionality Test: FAILED
)
if %load_test_result% equ 0 (
    echo Load Performance Test: PASSED
) else (
    echo Load Performance Test: FAILED
)
echo.

if %api_test_result% equ 0 if %load_test_result% equ 0 (
    echo ✅ All tests passed! Your Docker deployment is working perfectly.
) else (
    echo ⚠️  Some tests failed. Please check the details above.
)

goto :end

:invalid_choice
echo.
echo Invalid choice. Please select 1-6.
echo.
goto :start

:exit
echo.
echo Exiting...
goto :end

:end
echo.
echo ========================================
echo Testing Complete
echo ========================================
echo.
echo Additional commands you can use:
echo - View logs: docker-compose logs -f [service-name]
echo - Restart service: docker-compose restart [service-name]
echo - Stop all services: docker-compose down
echo - Monitor resources: docker stats
echo.
echo For continuous monitoring, run: node testing/docker-monitor.js
echo.
pause
