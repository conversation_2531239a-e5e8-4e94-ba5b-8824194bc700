@echo off
title ATMA Backend - Docker Testing Setup

echo ========================================
echo ATMA Backend - Docker Testing Setup
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available!
    pause
    exit /b 1
)

echo npm version:
npm --version
echo.

REM Install testing dependencies
echo Installing testing dependencies...
cd testing
npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies!
    echo Please check your internet connection and try again.
    cd ..
    pause
    exit /b 1
)

cd ..

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Testing dependencies installed successfully.
echo.
echo Available testing commands:
echo.
echo 1. Complete Test Suite:
echo    ./run-docker-tests.bat
echo.
echo 2. Individual Tests:
echo    ./docker-test.bat                    (Infrastructure)
echo    cd testing && npm run docker:test:api    (API Testing)
echo    cd testing && npm run docker:test:load   (Load Testing)
echo    cd testing && npm run docker:test:monitor (Monitoring)
echo.
echo 3. Manual Testing:
echo    cd testing
echo    node docker-api-test.js
echo    node docker-load-test.js
echo    node docker-monitor.js
echo.
echo Before running tests, make sure Docker services are running:
echo    docker-compose up -d
echo.
pause
