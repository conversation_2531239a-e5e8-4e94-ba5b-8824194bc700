# ATMA Backend - Docker Deployment Guide

## Overview

This guide explains how to deploy the ATMA (AI-Driven Talent Mapping Assessment) backend using Docker and Docker Compose.

## Prerequisites

- Docker Desktop installed and running
- Docker Compose v3.8 or higher
- At least 4GB RAM available for containers
- Ports 3000-3005, 5432, 6379, 5672, and 15672 available

## Architecture

The ATMA backend consists of the following services:

### Core Services
- **API Gateway** (Port 3000) - Main entry point, handles routing and authentication
- **Auth Service** (Port 3001) - User authentication and authorization
- **Archive Service** (Port 3002) - Stores analysis results
- **Assessment Service** (Port 3003) - Receives and queues assessment data
- **Notification Service** (Port 3005) - Real-time notifications via WebSocket

### Worker Services
- **Analysis Worker** (3 instances) - Processes assessment data using AI

### Infrastructure Services
- **PostgreSQL** (Port 5432) - Primary database
- **Redis** (Port 6379) - Caching and session storage
- **RabbitMQ** (Port 5672, Management UI: 15672) - Message broker

## Quick Start

1. **<PERSON>lone and navigate to the project directory:**
   ```bash
   cd atma-backend
   ```

2. **Start all services:**
   ```bash
   docker-compose up -d
   ```

3. **Check service status:**
   ```bash
   docker-compose ps
   ```

4. **View logs:**
   ```bash
   # All services
   docker-compose logs -f
   
   # Specific service
   docker-compose logs -f api-gateway
   ```

5. **Access the application:**
   - API Gateway: http://localhost:3000
   - RabbitMQ Management: http://localhost:15672 (guest/guest)

## Environment Configuration

The Docker Compose setup uses production-ready environment variables. Key configurations:

### Database
- **Host:** postgres (internal Docker network)
- **Database:** atma_db
- **User:** postgres
- **Password:** password

### Security
- **JWT Secret:** atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
- **Internal Service Key:** internal_service_secret_key_change_in_production

### Message Broker
- **RabbitMQ URL:** amqp://guest:guest@rabbitmq:5672

## Database Initialization

The PostgreSQL container automatically initializes the database using the `current_database_dump.sql` file. This includes:
- Creating schemas: auth, archive, assessment
- Setting up tables and relationships
- Inserting initial data

## Service Dependencies

Services start in the correct order based on dependencies:
1. Infrastructure services (PostgreSQL, Redis, RabbitMQ)
2. Auth Service
3. Archive Service
4. Assessment Service
5. Notification Service
6. Analysis Workers
7. API Gateway

## Scaling

To scale analysis workers:

```bash
# Scale to 5 workers
docker-compose up -d --scale analysis-worker-1=2 --scale analysis-worker-2=2 --scale analysis-worker-3=1
```

## Monitoring and Health Checks

All services include health checks:
- **Database:** PostgreSQL connection test
- **Redis:** Ping test
- **RabbitMQ:** Diagnostics ping
- **Services:** HTTP health endpoints

## Logs

Logs are persisted in the `logs/` directory of each service:
- `api-gateway/logs/`
- `auth-service/logs/`
- `archive-service/logs/`
- `assessment-service/logs/`
- `notification-service/logs/`
- `analysis-worker/logs/`

## Useful Commands

### Start services
```bash
docker-compose up -d
```

### Stop services
```bash
docker-compose down
```

### Restart a specific service
```bash
docker-compose restart api-gateway
```

### View service logs
```bash
docker-compose logs -f [service-name]
```

### Execute commands in containers
```bash
# Access PostgreSQL
docker-compose exec postgres psql -U postgres -d atma_db

# Access Redis CLI
docker-compose exec redis redis-cli

# Access service shell
docker-compose exec api-gateway sh
```

### Clean up
```bash
# Stop and remove containers, networks
docker-compose down

# Remove volumes (WARNING: This deletes all data)
docker-compose down -v

# Remove images
docker-compose down --rmi all
```

## Troubleshooting

### Services not starting
1. Check if ports are available: `netstat -an | findstr ":3000"`
2. Check Docker logs: `docker-compose logs [service-name]`
3. Verify Docker Desktop is running

### Database connection issues
1. Wait for PostgreSQL to fully initialize (check logs)
2. Verify database credentials in docker-compose.yml
3. Check if database dump loaded correctly

### Memory issues
1. Increase Docker Desktop memory allocation
2. Reduce number of analysis workers
3. Monitor container resource usage: `docker stats`

### Network issues
1. Check if containers are on the same network: `docker network ls`
2. Verify service names in environment variables
3. Test internal connectivity: `docker-compose exec api-gateway ping postgres`

## Production Considerations

For production deployment:

1. **Change default passwords and secrets**
2. **Use environment-specific configuration files**
3. **Set up proper logging aggregation**
4. **Configure backup strategies for PostgreSQL**
5. **Implement monitoring and alerting**
6. **Use Docker secrets for sensitive data**
7. **Set resource limits for containers**

## API Endpoints

Once deployed, the API Gateway exposes:
- `GET /health` - Health check
- `POST /auth/login` - User authentication
- `POST /auth/register` - User registration
- `POST /assessment/submit` - Submit assessment
- `GET /archive/results` - Get analysis results

For complete API documentation, refer to the individual service API documentation files.
