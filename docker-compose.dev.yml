version: '3.8'

services:
  # Database
  postgres:
    image: postgres:17
    container_name: atma-postgres-dev
    environment:
      POSTGRES_DB: atma_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./current_database_dump.sql:/docker-entrypoint-initdb.d/01-init.sql
    ports:
      - "5432:5432"
    networks:
      - atma-network-dev

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: atma-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - atma-network-dev

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: atma-rabbitmq-dev
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data_dev:/var/lib/rabbitmq
    networks:
      - atma-network-dev

volumes:
  postgres_data_dev:
  redis_data_dev:
  rabbitmq_data_dev:

networks:
  atma-network-dev:
    driver: bridge
