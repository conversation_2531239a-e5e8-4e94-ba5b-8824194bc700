@echo off
title ATMA Backend - Development Infrastructure

echo ========================================
echo ATMA Backend - Development Infrastructure
echo ========================================
echo.
echo Starting PostgreSQL, Redis, and RabbitMQ for development...
echo This allows you to run services locally while using Docker for infrastructure.
echo.

REM Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running or not installed!
    echo Please start Docker Desktop and try again.
    pause
    exit /b 1
)

REM Start infrastructure services only
docker-compose -f docker-compose.dev.yml up -d

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to start infrastructure services!
    echo Check the logs above for details.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Infrastructure Services Started!
echo ========================================
echo.
echo Available services:
echo - PostgreSQL: localhost:5432 (postgres/password)
echo - Redis: localhost:6379
echo - RabbitMQ: localhost:5672
echo - RabbitMQ Management: http://localhost:15672 (guest/guest)
echo.
echo You can now run your Node.js services locally using:
echo - npm run dev (in each service directory)
echo.
echo To stop infrastructure: docker-compose -f docker-compose.dev.yml down
echo.

REM Check service status
echo Checking service status...
docker-compose -f docker-compose.dev.yml ps

echo.
echo Press any key to exit...
pause >nul
