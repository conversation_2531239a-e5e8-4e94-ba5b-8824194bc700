@echo off
title ATMA Backend - Docker Deployment Testing

echo ========================================
echo ATMA Backend - Docker Deployment Testing
echo ========================================
echo.

REM Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running!
    pause
    exit /b 1
)

echo Starting comprehensive testing of Docker deployment...
echo.

REM Test 1: Check if all containers are running
echo [TEST 1] Checking container status...
echo ----------------------------------------
docker-compose ps
echo.

REM Count running containers
for /f %%i in ('docker-compose ps -q ^| find /c /v ""') do set CONTAINER_COUNT=%%i
echo Total containers: %CONTAINER_COUNT%

if %CONTAINER_COUNT% LSS 9 (
    echo ❌ ERROR: Not all containers are running!
    echo Expected: 9 containers (3 infrastructure + 6 services)
    echo Found: %CONTAINER_COUNT% containers
    echo.
    goto :error
) else (
    echo ✅ All containers are running
)
echo.

REM Test 2: Check container health
echo [TEST 2] Checking container health...
echo ----------------------------------------
docker-compose ps --format "table {{.Name}}\t{{.Status}}"
echo.

REM Test 3: Check database connectivity
echo [TEST 3] Testing database connectivity...
echo ----------------------------------------
docker-compose exec -T postgres pg_isready -U postgres -d atma_db
if %errorlevel% equ 0 (
    echo ✅ PostgreSQL is ready
) else (
    echo ❌ PostgreSQL connection failed
    goto :error
)
echo.

REM Test 4: Check Redis connectivity
echo [TEST 4] Testing Redis connectivity...
echo ----------------------------------------
docker-compose exec -T redis redis-cli ping
if %errorlevel% equ 0 (
    echo ✅ Redis is responding
) else (
    echo ❌ Redis connection failed
    goto :error
)
echo.

REM Test 5: Check RabbitMQ connectivity
echo [TEST 5] Testing RabbitMQ connectivity...
echo ----------------------------------------
docker-compose exec -T rabbitmq rabbitmq-diagnostics ping
if %errorlevel% equ 0 (
    echo ✅ RabbitMQ is responding
) else (
    echo ❌ RabbitMQ connection failed
    goto :error
)
echo.

REM Test 6: Test service health endpoints
echo [TEST 6] Testing service health endpoints...
echo ----------------------------------------

REM Wait for services to be fully ready
echo Waiting for services to initialize...
timeout /t 15 /nobreak >nul

REM Test API Gateway
echo Testing API Gateway (Port 3000)...
curl -s -o nul -w "%%{http_code}" http://localhost:3000/health | findstr "200" >nul
if %errorlevel% equ 0 (
    echo ✅ API Gateway health check passed
) else (
    echo ❌ API Gateway health check failed
    set TEST_FAILED=1
)

REM Test Auth Service
echo Testing Auth Service (Port 3001)...
curl -s -o nul -w "%%{http_code}" http://localhost:3001/health | findstr "200" >nul
if %errorlevel% equ 0 (
    echo ✅ Auth Service health check passed
) else (
    echo ❌ Auth Service health check failed
    set TEST_FAILED=1
)

REM Test Archive Service
echo Testing Archive Service (Port 3002)...
curl -s -o nul -w "%%{http_code}" http://localhost:3002/health | findstr "200" >nul
if %errorlevel% equ 0 (
    echo ✅ Archive Service health check passed
) else (
    echo ❌ Archive Service health check failed
    set TEST_FAILED=1
)

REM Test Assessment Service
echo Testing Assessment Service (Port 3003)...
curl -s -o nul -w "%%{http_code}" http://localhost:3003/health | findstr "200" >nul
if %errorlevel% equ 0 (
    echo ✅ Assessment Service health check passed
) else (
    echo ❌ Assessment Service health check failed
    set TEST_FAILED=1
)

REM Test Notification Service
echo Testing Notification Service (Port 3005)...
curl -s -o nul -w "%%{http_code}" http://localhost:3005/health | findstr "200" >nul
if %errorlevel% equ 0 (
    echo ✅ Notification Service health check passed
) else (
    echo ❌ Notification Service health check failed
    set TEST_FAILED=1
)

echo.

REM Test 7: Check database schemas
echo [TEST 7] Checking database schemas...
echo ----------------------------------------
docker-compose exec -T postgres psql -U postgres -d atma_db -c "\dn" | findstr "auth\|archive\|assessment" >nul
if %errorlevel% equ 0 (
    echo ✅ Database schemas exist (auth, archive, assessment)
) else (
    echo ❌ Database schemas missing
    set TEST_FAILED=1
)
echo.

REM Test 8: Check logs for errors
echo [TEST 8] Checking for critical errors in logs...
echo ----------------------------------------
docker-compose logs --tail=50 | findstr /i "error\|fatal\|exception" >nul
if %errorlevel% equ 0 (
    echo ⚠️  Warning: Found errors in logs (check details below)
    echo Recent errors:
    docker-compose logs --tail=20 | findstr /i "error\|fatal\|exception"
    echo.
) else (
    echo ✅ No critical errors found in recent logs
)
echo.

REM Test 9: Resource usage check
echo [TEST 9] Checking resource usage...
echo ----------------------------------------
echo Container resource usage:
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
echo.

REM Final results
echo ========================================
echo TEST RESULTS SUMMARY
echo ========================================
if defined TEST_FAILED (
    echo ❌ Some tests failed! Check the details above.
    echo.
    echo Troubleshooting steps:
    echo 1. Check service logs: docker-compose logs [service-name]
    echo 2. Restart failed services: docker-compose restart [service-name]
    echo 3. Check port conflicts: netstat -an ^| findstr ":3000\|:3001\|:3002\|:3003\|:3005"
    echo.
    goto :error
) else (
    echo ✅ All tests passed! Docker deployment is working correctly.
    echo.
    echo Your ATMA Backend is ready to use:
    echo - API Gateway: http://localhost:3000
    echo - RabbitMQ Management: http://localhost:15672 (guest/guest)
    echo.
)

echo Press any key to exit...
pause >nul
exit /b 0

:error
echo.
echo ========================================
echo DEPLOYMENT TESTING FAILED
echo ========================================
echo.
echo Please check the errors above and fix them before proceeding.
echo.
echo Useful commands:
echo - View all logs: docker-compose logs -f
echo - Restart services: docker-compose restart
echo - Stop and restart: docker-compose down && docker-compose up -d
echo.
pause
exit /b 1
