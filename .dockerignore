# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.example

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage
*.lcov
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Git
.git
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# IDE
.vscode
.idea

# OS
.DS_Store
Thumbs.db

# Testing
test-data
tests

# Documentation
*.md
docs/

# Scripts
scripts/
*.bat
*.sh
