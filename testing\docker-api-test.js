/**
 * ATMA Backend - Docker API Testing Script
 * Tests all API endpoints to ensure Docker deployment is working correctly
 */

const axios = require('axios');
const WebSocket = require('ws');

// Configuration
const config = {
  baseURL: 'http://localhost:3000',
  timeout: 10000,
  testUser: {
    username: 'docker-test-user',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    fullName: 'Docker Test User'
  }
};

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

// Helper function to log test results
function logTest(testName, passed, message = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: ${message}`);
  }
  testResults.details.push({ testName, passed, message });
}

// Helper function to make API requests
async function apiRequest(method, endpoint, data = null, headers = {}) {
  try {
    const response = await axios({
      method,
      url: `${config.baseURL}${endpoint}`,
      data,
      headers,
      timeout: config.timeout,
      validateStatus: () => true // Don't throw on HTTP error status
    });
    return response;
  } catch (error) {
    throw new Error(`Network error: ${error.message}`);
  }
}

// Test 1: Health Checks
async function testHealthChecks() {
  console.log('\n[TEST 1] Health Checks');
  console.log('='.repeat(50));

  const services = [
    { name: 'API Gateway', url: '/health' },
    { name: 'Auth Service', url: '/auth/health' },
    { name: 'Archive Service', url: '/archive/health' },
    { name: 'Assessment Service', url: '/assessment/health' }
  ];

  for (const service of services) {
    try {
      const response = await apiRequest('GET', service.url);
      logTest(
        `${service.name} Health Check`,
        response.status === 200,
        response.status !== 200 ? `Status: ${response.status}` : ''
      );
    } catch (error) {
      logTest(`${service.name} Health Check`, false, error.message);
    }
  }
}

// Test 2: Authentication Flow
async function testAuthenticationFlow() {
  console.log('\n[TEST 2] Authentication Flow');
  console.log('='.repeat(50));

  let authToken = null;

  // Test user registration
  try {
    const registerResponse = await apiRequest('POST', '/auth/register', config.testUser);
    logTest(
      'User Registration',
      registerResponse.status === 201 || registerResponse.status === 409, // 409 if user already exists
      registerResponse.status !== 201 && registerResponse.status !== 409 ? 
        `Status: ${registerResponse.status}, Message: ${registerResponse.data?.message}` : ''
    );
  } catch (error) {
    logTest('User Registration', false, error.message);
  }

  // Test user login
  try {
    const loginResponse = await apiRequest('POST', '/auth/login', {
      username: config.testUser.username,
      password: config.testUser.password
    });
    
    const loginSuccess = loginResponse.status === 200 && loginResponse.data?.token;
    logTest(
      'User Login',
      loginSuccess,
      !loginSuccess ? `Status: ${loginResponse.status}, Message: ${loginResponse.data?.message}` : ''
    );

    if (loginSuccess) {
      authToken = loginResponse.data.token;
    }
  } catch (error) {
    logTest('User Login', false, error.message);
  }

  // Test protected route access
  if (authToken) {
    try {
      const profileResponse = await apiRequest('GET', '/auth/profile', null, {
        'Authorization': `Bearer ${authToken}`
      });
      logTest(
        'Protected Route Access',
        profileResponse.status === 200,
        profileResponse.status !== 200 ? `Status: ${profileResponse.status}` : ''
      );
    } catch (error) {
      logTest('Protected Route Access', false, error.message);
    }
  } else {
    logTest('Protected Route Access', false, 'No auth token available');
  }

  return authToken;
}

// Test 3: Assessment Submission
async function testAssessmentSubmission(authToken) {
  console.log('\n[TEST 3] Assessment Submission');
  console.log('='.repeat(50));

  if (!authToken) {
    logTest('Assessment Submission', false, 'No auth token available');
    return;
  }

  const assessmentData = {
    assessmentName: 'Docker Test Assessment',
    candidateName: 'Test Candidate',
    candidateEmail: '<EMAIL>',
    responses: [
      {
        question: 'What is your experience with Docker?',
        answer: 'I have extensive experience with Docker containerization and orchestration.'
      },
      {
        question: 'Describe your programming skills.',
        answer: 'I am proficient in JavaScript, Node.js, and have experience with microservices architecture.'
      }
    ]
  };

  try {
    const response = await apiRequest('POST', '/assessment/submit', assessmentData, {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    });

    logTest(
      'Assessment Submission',
      response.status === 201 || response.status === 202,
      response.status !== 201 && response.status !== 202 ? 
        `Status: ${response.status}, Message: ${response.data?.message}` : ''
    );

    return response.data?.assessmentId;
  } catch (error) {
    logTest('Assessment Submission', false, error.message);
    return null;
  }
}

// Test 4: Archive Service
async function testArchiveService(authToken) {
  console.log('\n[TEST 4] Archive Service');
  console.log('='.repeat(50));

  if (!authToken) {
    logTest('Archive Service Access', false, 'No auth token available');
    return;
  }

  try {
    const response = await apiRequest('GET', '/archive/results?page=1&limit=5', null, {
      'Authorization': `Bearer ${authToken}`
    });

    logTest(
      'Archive Service Access',
      response.status === 200,
      response.status !== 200 ? `Status: ${response.status}` : ''
    );
  } catch (error) {
    logTest('Archive Service Access', false, error.message);
  }
}

// Test 5: WebSocket Connection
async function testWebSocketConnection(authToken) {
  console.log('\n[TEST 5] WebSocket Connection');
  console.log('='.repeat(50));

  return new Promise((resolve) => {
    if (!authToken) {
      logTest('WebSocket Connection', false, 'No auth token available');
      resolve();
      return;
    }

    try {
      const ws = new WebSocket('ws://localhost:3005', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      const timeout = setTimeout(() => {
        ws.close();
        logTest('WebSocket Connection', false, 'Connection timeout');
        resolve();
      }, 5000);

      ws.on('open', () => {
        clearTimeout(timeout);
        logTest('WebSocket Connection', true);
        ws.close();
        resolve();
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        logTest('WebSocket Connection', false, error.message);
        resolve();
      });
    } catch (error) {
      logTest('WebSocket Connection', false, error.message);
      resolve();
    }
  });
}

// Test 6: Database Integration
async function testDatabaseIntegration() {
  console.log('\n[TEST 6] Database Integration');
  console.log('='.repeat(50));

  // Test if we can access database through API
  try {
    const response = await apiRequest('GET', '/auth/health');
    const hasDbConnection = response.status === 200 && response.data?.database;
    
    logTest(
      'Database Connection via API',
      hasDbConnection,
      !hasDbConnection ? 'Database status not available in health check' : ''
    );
  } catch (error) {
    logTest('Database Connection via API', false, error.message);
  }
}

// Main test runner
async function runAllTests() {
  console.log('🐳 ATMA Backend Docker API Testing');
  console.log('='.repeat(60));
  console.log(`Testing against: ${config.baseURL}`);
  console.log(`Timeout: ${config.timeout}ms`);
  console.log('='.repeat(60));

  try {
    // Run all tests
    await testHealthChecks();
    const authToken = await testAuthenticationFlow();
    await testAssessmentSubmission(authToken);
    await testArchiveService(authToken);
    await testWebSocketConnection(authToken);
    await testDatabaseIntegration();

    // Print final results
    console.log('\n' + '='.repeat(60));
    console.log('🏁 TEST RESULTS SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${testResults.total}`);
    console.log(`Passed: ${testResults.passed} ✅`);
    console.log(`Failed: ${testResults.failed} ❌`);
    console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);

    if (testResults.failed === 0) {
      console.log('\n🎉 All tests passed! Docker deployment is working correctly.');
      process.exit(0);
    } else {
      console.log('\n⚠️  Some tests failed. Please check the details above.');
      console.log('\nFailed tests:');
      testResults.details
        .filter(test => !test.passed)
        .forEach(test => console.log(`  - ${test.testName}: ${test.message}`));
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 Test runner error:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = { runAllTests, testResults };
