# ATMA Backend - Docker Testing Guide

## Overview

Panduan lengkap untuk melakukan testing terhadap deployment Docker ATMA Backend. Testing ini memastikan bahwa semua service ber<PERSON>lan dengan baik, API berfungsi dengan benar, dan sistem dapat menangani beban kerja yang diharapkan.

## Prerequisites

- Docker Desktop running
- Node.js installed (untuk API testing)
- ATMA Backend services running (`docker-compose up -d`)
- curl atau PowerShell (untuk basic testing)

## Testing Structure

```
testing/
├── docker-api-test.js      # API functionality testing
├── docker-load-test.js     # Performance & load testing
├── docker-monitor.js       # Continuous monitoring
└── test-config.js          # Test configuration

Scripts:
├── docker-test.bat         # Infrastructure testing
├── run-docker-tests.bat    # Complete testing suite
└── docker-monitor.bat      # Monitoring dashboard
```

## 🚀 Quick Start

### Option 1: Complete Test Suite (Recommended)
```bash
# Run all tests automatically
./run-docker-tests.bat
```

### Option 2: Individual Tests
```bash
# Infrastructure test only
./docker-test.bat

# API functionality test
cd testing && node docker-api-test.js

# Load performance test
cd testing && node docker-load-test.js

# Continuous monitoring
cd testing && node docker-monitor.js
```

## 📋 Test Categories

### 1. Infrastructure Testing (`docker-test.bat`)

**What it tests:**
- ✅ Container status and health
- ✅ Database connectivity (PostgreSQL)
- ✅ Cache connectivity (Redis)
- ✅ Message broker connectivity (RabbitMQ)
- ✅ Service health endpoints
- ✅ Database schema validation
- ✅ Log analysis for errors
- ✅ Resource usage monitoring

**Expected Results:**
- 9 containers running (3 infrastructure + 6 services)
- All health checks passing
- No critical errors in logs

**Sample Output:**
```
[TEST 1] Checking container status...
✅ All containers are running

[TEST 2] Checking container health...
✅ PostgreSQL is ready
✅ Redis is responding
✅ RabbitMQ is responding

[TEST 6] Testing service health endpoints...
✅ API Gateway health check passed
✅ Auth Service health check passed
✅ Archive Service health check passed
✅ Assessment Service health check passed
✅ Notification Service health check passed
```

### 2. API Functionality Testing (`docker-api-test.js`)

**What it tests:**
- ✅ Service health endpoints
- ✅ User registration and authentication
- ✅ JWT token validation
- ✅ Assessment submission workflow
- ✅ Archive service data retrieval
- ✅ WebSocket connections
- ✅ Database integration through APIs

**Test Scenarios:**
1. **Health Checks** - All service endpoints respond correctly
2. **Authentication Flow** - Register → Login → Access protected routes
3. **Assessment Submission** - Submit assessment data through API
4. **Archive Access** - Retrieve stored results
5. **WebSocket Connection** - Real-time notification testing
6. **Database Integration** - Data persistence validation

**Sample Output:**
```
✅ API Gateway Health Check
✅ Auth Service Health Check
✅ User Registration
✅ User Login
✅ Protected Route Access
✅ Assessment Submission
✅ Archive Service Access
✅ WebSocket Connection

🏁 TEST RESULTS SUMMARY
Total Tests: 8
Passed: 8 ✅
Failed: 0 ❌
Success Rate: 100.0%
```

### 3. Load Performance Testing (`docker-load-test.js`)

**What it tests:**
- ⚡ Concurrent user handling
- ⚡ Response time under load
- ⚡ System throughput
- ⚡ Error rate under stress
- ⚡ Resource utilization
- ⚡ Scalability assessment

**Test Configuration:**
- **Concurrent Users:** 10 (configurable)
- **Requests per User:** 5 workflow steps
- **Test Duration:** ~30 seconds
- **Workflow:** Register → Login → Profile → Assessment → Archive

**Performance Metrics:**
- Total requests processed
- Success/failure rate
- Average response time
- Response time percentiles (50th, 95th, 99th)
- Requests per second
- Error analysis

**Sample Output:**
```
📈 LOAD TEST RESULTS
Total Test Time: 25.43s
Total Requests: 50
Successful Requests: 48
Failed Requests: 2
Success Rate: 96.00%
Requests per Second: 1.97

📊 Response Time Statistics:
Average: 245.67ms
Minimum: 89.23ms
Maximum: 1,234.56ms
50th Percentile: 198.45ms
95th Percentile: 567.89ms
99th Percentile: 1,123.45ms

🎯 Performance Assessment:
🟡 GOOD: System performance is acceptable
```

### 4. Continuous Monitoring (`docker-monitor.js`)

**What it monitors:**
- 📊 Real-time service health
- 📊 Container resource usage (CPU, Memory)
- 📊 Response time trends
- 📊 Error rate tracking
- 📊 Uptime statistics
- 📊 Alert generation

**Monitoring Dashboard:**
```
🐳 ATMA Backend Docker Monitoring Dashboard
================================================================================
Monitoring since: 12/7/2024, 2:30:15 PM
Total checks: 15
Last update: 12/7/2024, 2:37:45 PM

📊 Service Health Summary:
  🟢 API Gateway: 100.0% uptime, 156ms avg response
  🟢 Auth Service: 100.0% uptime, 89ms avg response
  🟢 Archive Service: 100.0% uptime, 134ms avg response
  🟢 Assessment Service: 100.0% uptime, 167ms avg response
  🟢 Notification Service: 100.0% uptime, 78ms avg response

🖥️  Container Performance:
  atma-api-gateway:
    🟢 CPU: 12.5%
    🟢 Memory: 145.2MiB (7.2%)
  atma-postgres:
    🟡 CPU: 35.8%
    🟢 Memory: 234.1MiB (11.6%)
```

## 🔧 Configuration

### Test Configuration (`testing/test-config.js`)
```javascript
module.exports = {
  baseURL: 'http://localhost:3000',
  timeout: 10000,
  loadTest: {
    concurrentUsers: 10,
    requestsPerUser: 5,
    rampUpTime: 2000
  },
  monitoring: {
    interval: 30000,
    alertThresholds: {
      responseTime: 2000,
      errorRate: 5,
      cpuUsage: 80,
      memoryUsage: 80
    }
  }
};
```

### Environment Variables
```bash
# Test environment
TEST_BASE_URL=http://localhost:3000
TEST_TIMEOUT=10000
TEST_CONCURRENT_USERS=10

# Monitoring
MONITOR_INTERVAL=30000
ALERT_RESPONSE_TIME=2000
ALERT_ERROR_RATE=5
```

## 📊 Performance Benchmarks

### Expected Performance Standards

| Metric | Good | Acceptable | Poor |
|--------|------|------------|------|
| Response Time (avg) | < 500ms | < 1000ms | > 1000ms |
| Success Rate | > 99% | > 95% | < 95% |
| CPU Usage | < 50% | < 80% | > 80% |
| Memory Usage | < 50% | < 80% | > 80% |
| Uptime | > 99.9% | > 99% | < 99% |

### Load Test Targets
- **Light Load:** 5 concurrent users
- **Normal Load:** 10 concurrent users
- **Heavy Load:** 25 concurrent users
- **Stress Test:** 50+ concurrent users

## 🚨 Troubleshooting

### Common Issues and Solutions

#### 1. Services Not Starting
```bash
# Check container status
docker-compose ps

# View service logs
docker-compose logs [service-name]

# Restart specific service
docker-compose restart [service-name]
```

#### 2. Health Check Failures
```bash
# Check if ports are available
netstat -an | findstr ":3000"

# Test direct service access
curl http://localhost:3001/health

# Check service dependencies
docker-compose logs postgres
```

#### 3. High Response Times
```bash
# Check resource usage
docker stats

# Analyze slow queries
docker-compose logs postgres | findstr "slow"

# Scale services if needed
docker-compose up -d --scale analysis-worker-1=2
```

#### 4. Memory Issues
```bash
# Check memory usage
docker stats --format "table {{.Container}}\t{{.MemUsage}}\t{{.MemPerc}}"

# Restart memory-heavy services
docker-compose restart analysis-worker-1

# Increase Docker memory allocation
# (Docker Desktop → Settings → Resources)
```

#### 5. Database Connection Issues
```bash
# Test database connectivity
docker-compose exec postgres pg_isready -U postgres

# Check database logs
docker-compose logs postgres

# Verify database initialization
docker-compose exec postgres psql -U postgres -d atma_db -c "\dn"
```

## 📈 Monitoring and Alerts

### Alert Types
- **Critical:** Service down, database unavailable
- **Warning:** High response time, high resource usage
- **Info:** Service restart, configuration change

### Alert Thresholds
- Response time > 2000ms
- Error rate > 5%
- CPU usage > 80%
- Memory usage > 80%

### Log Analysis
```bash
# Check for errors
docker-compose logs | grep -i error

# Monitor specific service
docker-compose logs -f api-gateway

# Check recent logs
docker-compose logs --tail=50
```

## 🎯 Best Practices

### Before Testing
1. Ensure Docker Desktop has sufficient resources (4GB+ RAM)
2. Close unnecessary applications
3. Verify all services are running
4. Check network connectivity

### During Testing
1. Monitor resource usage
2. Watch for error patterns
3. Note performance degradation
4. Document any issues

### After Testing
1. Analyze test results
2. Compare with benchmarks
3. Identify bottlenecks
4. Plan optimizations

## 📝 Test Reports

### Automated Reporting
Tests generate detailed reports including:
- Test execution summary
- Performance metrics
- Error analysis
- Resource utilization
- Recommendations

### Manual Verification
- Service accessibility via browser
- Database data integrity
- Log file analysis
- Resource monitoring

## 🔄 Continuous Integration

### Automated Testing Pipeline
```bash
# Pre-deployment testing
./docker-test.bat

# Post-deployment validation
node testing/docker-api-test.js

# Performance baseline
node testing/docker-load-test.js

# Continuous monitoring
node testing/docker-monitor.js &
```

### Scheduled Testing
- **Hourly:** Health checks
- **Daily:** API functionality tests
- **Weekly:** Load performance tests
- **Monthly:** Comprehensive test suite

## 📞 Support

Jika mengalami masalah dengan testing:

1. **Check logs:** `docker-compose logs -f`
2. **Restart services:** `docker-compose restart`
3. **Full reset:** `docker-compose down && docker-compose up -d`
4. **Resource check:** `docker stats`
5. **Network check:** `docker network ls`

Untuk bantuan lebih lanjut, periksa dokumentasi individual service atau hubungi tim development.
