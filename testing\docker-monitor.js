/**
 * ATMA Backend - Docker Monitoring Script
 * Continuous monitoring of Docker deployment health and performance
 */

const axios = require('axios');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Configuration
const config = {
  baseURL: 'http://localhost:3000',
  monitoringInterval: 30000, // 30 seconds
  alertThresholds: {
    responseTime: 2000, // ms
    errorRate: 5, // percentage
    cpuUsage: 80, // percentage
    memoryUsage: 80 // percentage
  },
  services: [
    { name: 'API Gateway', url: '/health', port: 3000 },
    { name: 'Auth Service', url: '/auth/health', port: 3001 },
    { name: 'Archive Service', url: '/archive/health', port: 3002 },
    { name: 'Assessment Service', url: '/assessment/health', port: 3003 },
    { name: 'Notification Service', url: '/notification/health', port: 3005 }
  ]
};

// Monitoring state
let monitoringData = {
  startTime: Date.now(),
  checks: 0,
  alerts: [],
  serviceStats: {},
  containerStats: {}
};

// Helper function to make health check request
async function checkServiceHealth(service) {
  const startTime = Date.now();
  
  try {
    const response = await axios.get(`${config.baseURL}${service.url}`, {
      timeout: 5000
    });
    
    const responseTime = Date.now() - startTime;
    
    return {
      name: service.name,
      status: 'healthy',
      responseTime,
      httpStatus: response.status,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    return {
      name: service.name,
      status: 'unhealthy',
      responseTime,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

// Get Docker container statistics
async function getContainerStats() {
  try {
    const { stdout } = await execAsync('docker stats --no-stream --format "table {{.Container}},{{.CPUPerc}},{{.MemUsage}},{{.MemPerc}},{{.NetIO}},{{.BlockIO}}"');
    
    const lines = stdout.trim().split('\n').slice(1); // Skip header
    const stats = {};
    
    lines.forEach(line => {
      const [container, cpu, memUsage, memPerc, netIO, blockIO] = line.split(',');
      
      if (container && container.startsWith('atma-')) {
        stats[container] = {
          cpu: parseFloat(cpu.replace('%', '')),
          memoryUsage: memUsage,
          memoryPercent: parseFloat(memPerc.replace('%', '')),
          networkIO: netIO,
          blockIO: blockIO,
          timestamp: new Date().toISOString()
        };
      }
    });
    
    return stats;
  } catch (error) {
    console.error('Error getting container stats:', error.message);
    return {};
  }
}

// Check if containers are running
async function checkContainerStatus() {
  try {
    const { stdout } = await execAsync('docker-compose ps --format "table {{.Name}},{{.State}},{{.Status}}"');
    
    const lines = stdout.trim().split('\n').slice(1); // Skip header
    const containerStatus = {};
    
    lines.forEach(line => {
      const [name, state, status] = line.split(',');
      if (name) {
        containerStatus[name] = {
          state: state || 'unknown',
          status: status || 'unknown',
          timestamp: new Date().toISOString()
        };
      }
    });
    
    return containerStatus;
  } catch (error) {
    console.error('Error checking container status:', error.message);
    return {};
  }
}

// Generate alert
function generateAlert(type, message, severity = 'warning') {
  const alert = {
    type,
    message,
    severity,
    timestamp: new Date().toISOString()
  };
  
  monitoringData.alerts.push(alert);
  
  // Keep only last 50 alerts
  if (monitoringData.alerts.length > 50) {
    monitoringData.alerts = monitoringData.alerts.slice(-50);
  }
  
  // Color-coded console output
  const colors = {
    critical: '\x1b[31m', // Red
    warning: '\x1b[33m',  // Yellow
    info: '\x1b[36m'      // Cyan
  };
  
  const resetColor = '\x1b[0m';
  const color = colors[severity] || colors.info;
  
  console.log(`${color}🚨 ALERT [${severity.toUpperCase()}]: ${message}${resetColor}`);
}

// Analyze service health
function analyzeServiceHealth(healthResults) {
  healthResults.forEach(result => {
    const serviceName = result.name;
    
    // Initialize service stats if not exists
    if (!monitoringData.serviceStats[serviceName]) {
      monitoringData.serviceStats[serviceName] = {
        totalChecks: 0,
        healthyChecks: 0,
        unhealthyChecks: 0,
        avgResponseTime: 0,
        responseTimes: []
      };
    }
    
    const stats = monitoringData.serviceStats[serviceName];
    stats.totalChecks++;
    
    if (result.status === 'healthy') {
      stats.healthyChecks++;
      stats.responseTimes.push(result.responseTime);
      
      // Keep only last 10 response times for average calculation
      if (stats.responseTimes.length > 10) {
        stats.responseTimes = stats.responseTimes.slice(-10);
      }
      
      stats.avgResponseTime = stats.responseTimes.reduce((a, b) => a + b, 0) / stats.responseTimes.length;
      
      // Check response time threshold
      if (result.responseTime > config.alertThresholds.responseTime) {
        generateAlert(
          'slow_response',
          `${serviceName} response time is ${result.responseTime}ms (threshold: ${config.alertThresholds.responseTime}ms)`,
          'warning'
        );
      }
    } else {
      stats.unhealthyChecks++;
      generateAlert(
        'service_down',
        `${serviceName} is unhealthy: ${result.error}`,
        'critical'
      );
    }
    
    // Check error rate
    const errorRate = (stats.unhealthyChecks / stats.totalChecks) * 100;
    if (errorRate > config.alertThresholds.errorRate && stats.totalChecks >= 5) {
      generateAlert(
        'high_error_rate',
        `${serviceName} error rate is ${errorRate.toFixed(1)}% (threshold: ${config.alertThresholds.errorRate}%)`,
        'warning'
      );
    }
  });
}

// Analyze container performance
function analyzeContainerPerformance(containerStats) {
  Object.entries(containerStats).forEach(([containerName, stats]) => {
    // Check CPU usage
    if (stats.cpu > config.alertThresholds.cpuUsage) {
      generateAlert(
        'high_cpu',
        `${containerName} CPU usage is ${stats.cpu}% (threshold: ${config.alertThresholds.cpuUsage}%)`,
        'warning'
      );
    }
    
    // Check memory usage
    if (stats.memoryPercent > config.alertThresholds.memoryUsage) {
      generateAlert(
        'high_memory',
        `${containerName} memory usage is ${stats.memoryPercent}% (threshold: ${config.alertThresholds.memoryUsage}%)`,
        'warning'
      );
    }
  });
  
  monitoringData.containerStats = containerStats;
}

// Display monitoring dashboard
function displayDashboard() {
  console.clear();
  console.log('🐳 ATMA Backend Docker Monitoring Dashboard');
  console.log('='.repeat(80));
  console.log(`Monitoring since: ${new Date(monitoringData.startTime).toLocaleString()}`);
  console.log(`Total checks: ${monitoringData.checks}`);
  console.log(`Last update: ${new Date().toLocaleString()}`);
  console.log('='.repeat(80));
  
  // Service Health Summary
  console.log('\n📊 Service Health Summary:');
  Object.entries(monitoringData.serviceStats).forEach(([serviceName, stats]) => {
    const uptime = ((stats.healthyChecks / stats.totalChecks) * 100).toFixed(1);
    const avgResponse = stats.avgResponseTime.toFixed(0);
    const status = stats.healthyChecks === stats.totalChecks ? '🟢' : 
                  uptime >= 95 ? '🟡' : '🔴';
    
    console.log(`  ${status} ${serviceName}: ${uptime}% uptime, ${avgResponse}ms avg response`);
  });
  
  // Container Performance
  console.log('\n🖥️  Container Performance:');
  Object.entries(monitoringData.containerStats).forEach(([containerName, stats]) => {
    const cpuStatus = stats.cpu > 50 ? '🔴' : stats.cpu > 25 ? '🟡' : '🟢';
    const memStatus = stats.memoryPercent > 50 ? '🔴' : stats.memoryPercent > 25 ? '🟡' : '🟢';
    
    console.log(`  ${containerName}:`);
    console.log(`    ${cpuStatus} CPU: ${stats.cpu.toFixed(1)}%`);
    console.log(`    ${memStatus} Memory: ${stats.memoryUsage} (${stats.memoryPercent.toFixed(1)}%)`);
  });
  
  // Recent Alerts
  if (monitoringData.alerts.length > 0) {
    console.log('\n🚨 Recent Alerts (last 5):');
    const recentAlerts = monitoringData.alerts.slice(-5);
    recentAlerts.forEach(alert => {
      const time = new Date(alert.timestamp).toLocaleTimeString();
      console.log(`  [${time}] ${alert.severity.toUpperCase()}: ${alert.message}`);
    });
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('Press Ctrl+C to stop monitoring');
}

// Main monitoring loop
async function runMonitoring() {
  console.log('🔍 Starting ATMA Backend Docker monitoring...');
  console.log(`Monitoring interval: ${config.monitoringInterval / 1000}s`);
  console.log('Press Ctrl+C to stop\n');
  
  const monitoringLoop = async () => {
    try {
      monitoringData.checks++;
      
      // Check service health
      const healthPromises = config.services.map(service => checkServiceHealth(service));
      const healthResults = await Promise.all(healthPromises);
      
      // Get container stats
      const containerStats = await getContainerStats();
      
      // Check container status
      const containerStatus = await checkContainerStatus();
      
      // Analyze results
      analyzeServiceHealth(healthResults);
      analyzeContainerPerformance(containerStats);
      
      // Display dashboard
      displayDashboard();
      
    } catch (error) {
      console.error('Monitoring error:', error.message);
    }
  };
  
  // Initial check
  await monitoringLoop();
  
  // Set up interval
  const intervalId = setInterval(monitoringLoop, config.monitoringInterval);
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n\n🛑 Stopping monitoring...');
    clearInterval(intervalId);
    
    // Display final summary
    console.log('\n📋 Final Monitoring Summary:');
    console.log(`Total monitoring time: ${((Date.now() - monitoringData.startTime) / 1000 / 60).toFixed(1)} minutes`);
    console.log(`Total checks performed: ${monitoringData.checks}`);
    console.log(`Total alerts generated: ${monitoringData.alerts.length}`);
    
    process.exit(0);
  });
}

// Run monitoring if this file is executed directly
if (require.main === module) {
  runMonitoring().catch(error => {
    console.error('Failed to start monitoring:', error.message);
    process.exit(1);
  });
}

module.exports = { runMonitoring, monitoringData };
