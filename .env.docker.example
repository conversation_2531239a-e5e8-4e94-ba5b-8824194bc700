# ATMA Backend - Docker Environment Configuration
# Copy this file to .env.docker and modify as needed for your deployment

# ===========================================
# GLOBAL CONFIGURATION
# ===========================================

# Environment
NODE_ENV=production

# Security Keys (CHANGE THESE IN PRODUCTION!)
JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# ===========================================
# DATABASE CONFIGURATION
# ===========================================

DB_HOST=postgres
DB_PORT=5432
DB_NAME=atma_db
DB_USER=postgres
DB_PASSWORD=password
DB_DIALECT=postgres

# Database Pool Settings
DB_POOL_MAX=25
DB_POOL_MIN=5
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=20000

# ===========================================
# REDIS CONFIGURATION
# ===========================================

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=atma:

# Cache Settings
CACHE_TTL_USER=3600
CACHE_TTL_JWT=1800
CACHE_TTL_SESSION=7200
ENABLE_CACHE=true

# ===========================================
# RABBITMQ CONFIGURATION
# ===========================================

RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest

# Queue Configuration
QUEUE_NAME=assessment_analysis
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=analysis.process
EVENTS_EXCHANGE_NAME=atma_events_exchange

# ===========================================
# SERVICE URLS (Internal Docker Network)
# ===========================================

AUTH_SERVICE_URL=http://auth-service:3001
ARCHIVE_SERVICE_URL=http://archive-service:3002
ASSESSMENT_SERVICE_URL=http://assessment-service:3003
NOTIFICATION_SERVICE_URL=http://notification-service:3005

# ===========================================
# SERVICE PORTS
# ===========================================

API_GATEWAY_PORT=3000
AUTH_SERVICE_PORT=3001
ARCHIVE_SERVICE_PORT=3002
ASSESSMENT_SERVICE_PORT=3003
NOTIFICATION_SERVICE_PORT=3005

# ===========================================
# LOGGING CONFIGURATION
# ===========================================

LOG_LEVEL=info
LOG_FORMAT=combined

# ===========================================
# RATE LIMITING
# ===========================================

RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5000

# ===========================================
# CORS CONFIGURATION
# ===========================================

ALLOWED_ORIGINS=*
CORS_ORIGIN=*

# ===========================================
# WORKER CONFIGURATION
# ===========================================

WORKER_CONCURRENCY=5
MAX_RETRIES=3
RETRY_DELAY=5000
PROCESSING_TIMEOUT=1800000

# ===========================================
# AUTHENTICATION CONFIGURATION
# ===========================================

JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=10
DEFAULT_TOKEN_BALANCE=5

# ===========================================
# GOOGLE AI CONFIGURATION (for Analysis Worker)
# ===========================================

# Add your Google AI API key here
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# AI Model Configuration
AI_MODEL=gemini-1.5-flash
MAX_TOKENS=8192
TEMPERATURE=0.7

# ===========================================
# HEALTH CHECK CONFIGURATION
# ===========================================

HEALTH_CHECK_INTERVAL=30000
SERVICE_TIMEOUT=5000

# ===========================================
# BATCH PROCESSING CONFIGURATION
# ===========================================

BATCH_MAX_SIZE=100
BATCH_TIMEOUT=2000
BATCH_MAX_QUEUE_SIZE=2000

# ===========================================
# SOCKET.IO CONFIGURATION
# ===========================================

SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000

# ===========================================
# PAGINATION CONFIGURATION
# ===========================================

DEFAULT_PAGE_SIZE=10
MAX_PAGE_SIZE=100
